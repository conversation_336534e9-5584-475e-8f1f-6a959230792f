#include "saadc.h"
#include "nrf_drv_saadc.h"



static void saadc_callback(nrf_drv_saadc_evt_t const * p_event)
{
    UNUSED_PARAMETER(p_event);
}


//SAADC初始化
void saadc_init(void)
{
    ret_code_t ret_code;

    ret_code = nrf_drv_saadc_init(NULL, saadc_callback);
    APP_ERROR_CHECK(ret_code);

    nrf_saadc_channel_config_t channel_config_3 = NRF_DRV_SAADC_DEFAULT_CHANNEL_CONFIG_SE(ADC_AMP1_PIN);	//ADC_AMP1_PIN
    ret_code = nrf_drv_saadc_channel_init(ADC_AMP1_CHANNEL, &channel_config_3);
    APP_ERROR_CHECK(ret_code);

    nrf_saadc_channel_config_t channel_config_4 = NRF_DRV_SAADC_DEFAULT_CHANNEL_CONFIG_SE(ADC_AMP2_PIN);	//ADC_AMP2_PIN
    ret_code = nrf_drv_saadc_channel_init(ADC_AMP2_CHANNEL, &channel_config_4);
    APP_ERROR_CHECK(ret_code);
}


//SAADC反初始化
void saadc_deinit(void)
{
    uint32_t err_code;

    while (true == nrf_drv_saadc_is_busy());
    err_code = nrf_drv_saadc_channel_uninit(ADC_AMP1_CHANNEL);
    APP_ERROR_CHECK(err_code);

    while (true == nrf_drv_saadc_is_busy());
    err_code = nrf_drv_saadc_channel_uninit(ADC_AMP2_CHANNEL);
    APP_ERROR_CHECK(err_code);

    while (true == nrf_drv_saadc_is_busy());
    nrf_drv_saadc_uninit();
    nrf_gpio_cfg_input(ADC_AMP1_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(ADC_AMP2_PIN, NRF_GPIO_PIN_NOPULL);
}


//ADC采样，返回ADC输入口信号的电压值，单位mV
//times为1耗时25us
uint32_t saadc_convert(saadc_channel_t channel, uint32_t times)
{
    APP_ERROR_CHECK_BOOL(times <= 1000);

    uint32_t sum = 0;

    for (uint32_t i = 0; i < times; i++) {
        nrf_saadc_value_t adc_value;//电压与ADC值对应：

        // 检查ADC是否忙碌，如果忙碌则等待，但限制等待时间
        uint32_t timeout_count = 0;
        while (nrf_drv_saadc_is_busy() && timeout_count < 1000) {
            timeout_count++;
            nrf_delay_us(1);
        }

        uint32_t err_code = nrf_drv_saadc_sample_convert((uint8_t)channel, &adc_value);//单次AD采样，阻塞
        APP_ERROR_CHECK(err_code);
        if (adc_value < 0) adc_value = 0;
        sum += adc_value;

        // 减少延时时间，避免长时间阻塞
        nrf_delay_us(50);
    }

    uint32_t mv = (uint32_t)sum * 3600 / 4096 / times;
    return mv;
}
//温度校准
void saadc_calibrate_offset(void)
{
    nrfx_saadc_calibrate_offset();   //ADC温度自动校准
    while (nrf_drv_saadc_is_busy());   //等待校准完成
}
